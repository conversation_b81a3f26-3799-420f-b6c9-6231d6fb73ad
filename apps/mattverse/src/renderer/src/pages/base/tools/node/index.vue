<template>
  <div class="p-6 space-y-6">
    <div class="flex items-center gap-2">
      <h1 class="text-xl font-semibold text-foreground/90">工具模块分组</h1>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-5">
      <Card
        v-for="module in moduleList"
        :key="module.key"
        class="group hover:shadow-lg transition-all duration-300 relative overflow-hidden border border-border/40 bg-gradient-to-br from-card to-card/95"
      >
        <div
          class="absolute inset-0 bg-gradient-to-r from-primary/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"
        ></div>

        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle
            class="text-lg font-medium group-hover:text-primary transition-colors duration-300"
          >
            {{ module.name }}
          </CardTitle>
          <div
            class="rounded-full bg-primary/10 p-2.5 transition-all duration-300 group-hover:scale-110 group-hover:bg-primary/20"
          >
            <MattSvg
              v-if="isSvg(module.icon)"
              :name="module.icon.value"
              class-name="w-5 h-5"
              :title="`SVG: ${module.icon.value}`"
            />
            <MattIcon
              v-else-if="isIcon(module.icon)"
              :name="module.icon.value"
              class="w-5 h-5 text-primary"
              :title="`Icon: ${module.icon.value}`"
            />
            <span v-else class="text-base" title="Default icon">🔧</span>
          </div>
        </CardHeader>

        <CardContent>
          <p
            class="text-sm text-muted-foreground line-clamp-2 h-10 group-hover:text-foreground/90 transition-colors duration-300"
          >
            {{ module.description || '暂无描述' }}
          </p>
          <div class="flex items-center justify-between mt-4">
            <span
              class="text-sm font-medium text-gray-500 group-hover:text-gray-700 transition-colors duration-300"
            >
              是否启用
            </span>
            <Switch
              :checked="!!module.enabled"
              class="transition-all duration-300"
              @update:checked="v => onToggle(module.key, v)"
            />
          </div>
        </CardContent>

        <!-- 删除按钮 -->
        <Button
          v-if="!module.isBuiltin"
          variant="destructive"
          size="icon"
          class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-all duration-300 scale-90 group-hover:scale-100 hover:bg-destructive/90"
          @click="openDeleteDialog(module.key)"
        >
          <Trash class="h-4 w-4" />
        </Button>

        <!-- 内置模块标记 -->
        <Badge
          v-if="module.isBuiltin"
          class="absolute top-1 left-2 bg-gray-400/80 backdrop-blur-sm transition-all duration-300 group-hover:bg-gray-500"
        >
          内置
        </Badge>
      </Card>
    </div>

    <!-- 删除确认对话框 -->
    <AlertDialog :open="isDeleteDialogOpen" @update:open="isDeleteDialogOpen = $event">
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>确认删除</AlertDialogTitle>
          <AlertDialogDescription>
            您确定要删除"{{ moduleToDelete }}"工具模块吗？此操作不可撤销。
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel @click="isDeleteDialogOpen = false">取消</AlertDialogCancel>
          <AlertDialogAction @click="confirmDelete">确认删除</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { storeToRefs } from 'pinia'
import { useNodeModulesStore } from '@/store'
import { Trash } from 'lucide-vue-next'
import { toast } from 'vue-sonner'

const toolsStore = useNodeModulesStore()
const { getModuleList: moduleList } = storeToRefs(toolsStore)

// 删除对话框状态
const isDeleteDialogOpen = ref(false)
const moduleToDelete = ref('')

function onToggle(name: string, value: boolean) {
  toolsStore.setModuleEnabled(name, value)
  toast.info(value ? '已启用' : '已禁用', {
    description: `${name} 已${value ? '启用' : '禁用'}`,
  })
}

function isSvg(icon: any): icon is { type: 'svg'; value: string } {
  return icon && typeof icon === 'object' && icon.type === 'svg' && typeof icon.value === 'string'
}

function isIcon(icon: any): icon is { type: 'icon'; value: string } {
  return icon && typeof icon === 'object' && icon.type === 'icon' && typeof icon.value === 'string'
}

// 打开删除对话框
const openDeleteDialog = (moduleKey: string) => {
  moduleToDelete.value = moduleKey
  isDeleteDialogOpen.value = true
}

// 确认删除
const confirmDelete = () => {
  // 这里应该调用删除模块的方法
  // toolsStore.deleteModule(moduleToDelete.value)

  toast.success('删除成功', {
    description: `成功删除工具模块 ${moduleToDelete.value}`,
  })

  isDeleteDialogOpen.value = false
  moduleToDelete.value = ''
}
</script>

<style lang="scss" scoped>
.card {
  @apply backdrop-blur-sm;

  &:hover {
    @apply shadow-md shadow-primary/5;
  }
}

:deep(.card) {
  @apply transition-all duration-300 hover:-translate-y-[2px] hover:shadow-md;
}
</style>
